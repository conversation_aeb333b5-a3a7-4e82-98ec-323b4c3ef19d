package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"tesseract-service/internal/config"
)

// UserService handles user-related operations
type UserService struct {
	config     *config.Config
	httpClient *http.Client
}

// UserProfile represents a user profile from the user service
type UserProfile struct {
	CreatedAtTs                      int64                            `json:"createdAtTs"`
	Department                       *string                          `json:"department"`
	Email                            string                           `json:"email"`
	FirstName                        string                           `json:"firstName"`
	IsActive                         bool                             `json:"isActive"`
	IsExistingUser                   bool                             `json:"isExistingUser"`
	KycStatus                        string                           `json:"kycStatus"`
	LastName                         string                           `json:"lastName"`
	OfficeD365Id                     string                           `json:"officeD365Id"`
	PhoneNumber                      string                           `json:"phoneNumber"`
	Profession                       *string                          `json:"profession"`
	TenantInfo                       TenantInfo                       `json:"tenantInfo"`
	UserAliasId                      string                           `json:"userAliasId"`
	UserPermissionAndEntityPayload   UserPermissionAndEntityPayload   `json:"userPermissionAndEntityPayload"`
	UserTypeDetails                  UserTypeDetails                  `json:"userTypeDetails"`
}

// TenantInfo represents tenant information
type TenantInfo struct {
	DefaultTenantId string   `json:"defaultTenantId"`
	Tenants         []Tenant `json:"tenants"`
}

// Tenant represents a tenant
type Tenant struct {
	TenantId   string `json:"tenantId"`
	TenantName string `json:"tenantName"`
}

// UserPermissionAndEntityPayload represents user permissions and entities
type UserPermissionAndEntityPayload struct {
	BusinessUnit    []string                `json:"businessUnit"`
	CustomerType    *string                 `json:"customerType"`
	Entities        map[string]interface{}  `json:"entities"`
	Facets          map[string]interface{}  `json:"facets"`
	Permissions     []Permission            `json:"permissions"`
	Roles           []string                `json:"roles"`
	TerritoryPayload []interface{}          `json:"territoryPayload"`
	UserId          int                     `json:"userId"`
}

// Permission represents a permission
type Permission struct {
	Level              string  `json:"level"`
	Name               string  `json:"name"`
	ParentPermissionId *string `json:"parentPermissionId"`
}

// UserTypeDetails represents user type details
type UserTypeDetails struct {
	MasterType   string `json:"masterType"`
	MasterTypeId string `json:"masterTypeId"`
	Type         string `json:"type"`
	TypeId       string `json:"typeId"`
}

// ValidateTokenRequest represents the request to validate a token
type ValidateTokenRequest struct {
	Token string `json:"token"`
	Email string `json:"email"`
}

// ValidateTokenResponse represents the response from token validation
type ValidateTokenResponse struct {
	Valid   bool         `json:"valid"`
	User    *UserProfile `json:"user,omitempty"`
	Message string       `json:"message,omitempty"`
}

// NewUserService creates a new user service
func NewUserService(cfg *config.Config) *UserService {
	return &UserService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ValidateTokenWithUserService validates a token with the user service
func (s *UserService) ValidateTokenWithUserService(token, email string) (*UserProfile, error) {
	// Prepare request
	reqBody := ValidateTokenRequest{
		Token: token,
		Email: email,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Make request to user service
	url := fmt.Sprintf("%s/api/v1/auth/validate", s.config.UserServiceURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request to user service: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("user service returned error: %s", string(body))
	}

	var response ValidateTokenResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if !response.Valid {
		return nil, fmt.Errorf("token validation failed: %s", response.Message)
	}

	return response.User, nil
}

// GetUserProfile gets user profile by email
func (s *UserService) GetUserProfile(email string) (*UserProfile, error) {
	// For now, we'll return a mock profile
	// In production, this would make a call to the user service
	return s.getMockUserProfile(email), nil
}

// getMockUserProfile returns a mock user profile for testing
func (s *UserService) getMockUserProfile(email string) *UserProfile {
	return &UserProfile{
		CreatedAtTs:    1700476127,
		Department:     nil,
		Email:          email,
		FirstName:      "Test",
		IsActive:       true,
		IsExistingUser: false,
		KycStatus:      "not_applicable",
		LastName:       "User",
		OfficeD365Id:   "",
		PhoneNumber:    "1234567890",
		Profession:     nil,
		TenantInfo: TenantInfo{
			DefaultTenantId: "3444cbc4-0104-59b5-b591-4fe39646cb51",
			Tenants: []Tenant{
				{
					TenantId:   "3444cbc4-0104-59b5-b591-4fe39646cb51",
					TenantName: "INFRAMARKET",
				},
			},
		},
		UserAliasId: "TESTUSER001",
		UserPermissionAndEntityPayload: UserPermissionAndEntityPayload{
			BusinessUnit: []string{"Sales", "Risk", "Supply Chain"},
			CustomerType: nil,
			Entities:     make(map[string]interface{}),
			Facets:       make(map[string]interface{}),
			Permissions: []Permission{
				{Level: "module", Name: "ANALYTICS_ACCESS", ParentPermissionId: nil},
			},
			Roles: []string{
				"RISK_CREDITRISK_ANALYTICS_PERFORMANCE_ANALYTICS",
				"SALES_SALESPRIMARY_ANALYTICS_PERFORMANCE_ANALYTICS",
				"SUPPLYCHAIN_INVENTORY_ANALYTICS_PERFORMANCE_ANALYTICS",
			},
			TerritoryPayload: []interface{}{},
			UserId:           12345,
		},
		UserTypeDetails: UserTypeDetails{
			MasterType:   "employee",
			MasterTypeId: "EMP001",
			Type:         "internal",
			TypeId:       "EMP001",
		},
	}
}
