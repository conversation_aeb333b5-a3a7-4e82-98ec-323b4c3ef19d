package providers

import (
	"flag"
	"os"

	"github.com/kelseyhightower/envconfig"
	yaml "gopkg.in/yaml.v3"
)

type Jira struct {
	BaseUrl      string `yaml:"baseUrl" envconfig:"baseUrl"`
	JiraUsername string `yaml:"username" envconfig:"username"`
	JiraToken    string `yaml:"apiToken" envconfig:"apiToken"`
}

type DbConfig struct {
	Host            string `yaml:"host" envconfig:"DB_HOST"`
	Port            int    `yaml:"port" envconfig:"DB_PORT"`
	User            string `yaml:"user" envconfig:"DB_USER"`
	Password        string `yaml:"password" envconfig:"DB_PASSWORD"`
	Name            string `yaml:"name" envconfig:"DB_NAME"`
	Timeout         int    `yaml:"timeout" envconfig:"DB_CONNECTION_TIMEOUT"`
	MaxOpenConn     int    `yaml:"maxOpenConn" envconfig:"DB_MAX_OPEN_CONNECTIONS"`
	MaxIdleConn     int    `yaml:"maxIdleConn" envconfig:"DB_MAX_IDLE_CONNECTIONS"`
	SearchPath      string `yaml:"searchPath" envconfig:"DB_SEARCH_PATH"`
	ConnMaxLifetime string `yaml:"connMaxLifetime" envconfig:"DB_CONNECTION_MAX_LIFETIME"`
}

type RedisConfig struct {
	Address             string `yaml:"address" envconfig:"REDIS_LISTEN_ADDRESS"`
	DbNumber            int    `yaml:"dbNumber" envconfig:"REDIS_DB_NUMBER"`
	MaxActiveConnection string `yaml:"maxActiveConnection" envvonfig:"REDIS_MAX_ACTIVE_CONNECTION"`
	MaxIdleConnection   string `yaml:"maxIdleConnection" envconfig:"REDIS_MAX_IDLE_CONNECTION"`
	MinIdleConnection   string `yaml:"minIdleConnection" envconfig:"REDIS_MIN_IDLE_CONNECTIONS"`
}

type LogglyConfig struct {
	Token         string `yaml:"token" envconfig:"LOGGLY_TOKEN"`
	Host          string `yaml:"host" envconfig:"LOGGLY_HOST"`
	Tag           string `yaml:"tag" envconfig:"LOGGLY_TAG"`
	AppName       string `yaml:"appName"  envconfig:"LOGGLY_APP_NAME"`
	AppVersion    string `yaml:"appVersion" envconfig:"LOGGLY_APP_VERSION"`
	GraylogHost   string `yaml:"graylogHost" envconfig:"GRAYLOG_HOST"`
	GraylogSwitch string `yaml:"graylogSwitch" envconfig:"GRAYLOG_SWITCH"`
}

type SlackConfig struct {
	Token string `yaml:"token" envconfig:"SLACK_WEBHOOK_TOKEN"`
}

type HttpConfig struct {
	Address        string   `yaml:"address" envconfig:"LISTEN_ADDRESS"`
	AllowedOrigins []string `yaml:"allowedOrigins" envconfig:"ALLOW_ORIGIN"`
}

type JwtConfig struct {
	Secret string `yaml:"secret" envconfig:"JWT_SECRET_KEY"`
}

type GrpcConfig struct {
	ListenAddress              string `yaml:"listenAddress" envconfig:"GRPC_LISTEN_ADDRESS"`
	KycListenAddress           string `yaml:"kycListenAddress" envconfig:"KYC_GRPC_LISTEN_ADDRESS"`
	InfluencerListenAddress    string `yaml:"influencerListenAddress" envconfig:"INFLUENCER_GRPC_LISTEN_ADDRESS"`
	SearchListenAddress        string `yaml:"searchListenAddress" envconfig:"SEARCH_GRPC_LISTEN_ADDRESS"`
	IdentityListenAddress      string `yaml:"identityListenAddress" envconfig:"IDENTITY_GRPC_LISTEN_ADDRESS"`
	TicketServiceListenAddress string `yaml:"ticketServiceListenAddress" envconfig:"TICKET_SERVICE_GRPC_LISTEN_ADDRESS"`
	CatalogListenAddress       string `yaml:"catalog_listen_address" envconfig:"PRODUCT_GRPC_LISTEN_ADDRESS"`
	LocationListenAddress      string `yaml:"location_listen_address" envconfig:"LOCATION_GRPC_LISTEN_ADDRESS"`
	StoreListenAddress         string `yaml:"store_listen_address" envconfig:"STORE_GRPC_LISTEN_ADDRESS"`
}

type KafkaConfig struct {
	KafkaBrokerUrls  []string    `yaml:"kafkaBrokerUrls" envconfig:"KAFKA_BROKER_URLS"`
	KafkaBatchSize   int         `yaml:"kafkaBatchSize" envconfig:"KAFKA_BATCH_SIZE"`
	Environment      string      `yaml:"environment" envconfig:"KAFKA_ENV"`
	UserServiceKafka UserService `yaml:"user_service_kafka"`
}

type EnvironmentConfig struct {
	Name string `yaml:"name" envconfig:"ENV_NAME"`
}

type MetricsConfig struct {
	WhiteListedEndpoints              []string `yaml:"whiteListEndpoints"`
	WhiteListedAuthorizationEndpoints []string `yaml:"whiteListedAuthorizationEndpoints"`
	WhiteListedEnvironments           []string `yaml:"whiteListEnvironments"`
}

type AuthorizationConfig struct {
	PermissionFile         string   `yaml:"authorizationPermissionFile"`
	AuthorizationEndpoints []string `yaml:"authorizationEndpoints"`
}

type CommunicationKafka struct {
	MainTopic     string `yaml:"in_main_topic" envconfig:"IN_MAIN_TOPIC"`
	SourceService string `yaml:"in_source_service" envconfig:"IN_SERVICE"`
	GroupId       string `yaml:"in_group_id" envconfig:"IN_GROUP_ID"`
	OutboxTable   string `yaml:"in_outbox_table" envconfig:"IN_OUTBOX_TABLE"`
}

type UserService struct {
	MainTopic     string `yaml:"in_main_topic" envconfig:"IN_MAIN_TOPIC"`
	SourceService string `yaml:"in_source_service" envconfig:"IN_SERVICE"`
	GroupId       string `yaml:"in_group_id" envconfig:"IN_GROUP_ID"`
	OutboxTable   string `yaml:"in_outbox_table" envconfig:"IN_OUTBOX_TABLE"`
}

type S3Config struct {
	AwsAccessKeyId     string `yaml:"aws_access_key_id" envconfig:"AWS_ACCESS_KEY_ID"`
	AwsSecretAccessKey string `yaml:"aws_secret_access_key" envconfig:"AWS_SECRET_ACCESS_KEY"`
	AwsRegion          string `yaml:"aws_region" envconfig:"AWS_REGION"`
	AwsToken           string `yaml:"aws_token" envconfig:"AWS_TOKEN"`
}

type UserBlockingConfig struct {
	AppId            string `yaml:"app_id" envconfig:"APP_ID"`
	MaxRetryAttempts int    `yaml:"max_retry_attempts" envconfig:"MAX_RETRY_ATTEMPTS"`
	BlockingTime     int64  `yaml:"blocking_time" envconfig:"BLOCKING_TIME"`
}

type AppConfig struct {
	DbConfig               DbConfig                        `yaml:"db"`
	Jira                   Jira                            `yaml:"jira"`
	RedisConfig            RedisConfig                     `yaml:"redis"`
	LogglyConfig           LogglyConfig                    `yaml:"loggly"`
	SlackConfig            SlackConfig                     `yaml:"slack"`
	HttpConfig             HttpConfig                      `yaml:"http"`
	JwtConfig              JwtConfig                       `yaml:"jwt"`
	GrpcConfig             GrpcConfig                      `yaml:"grpc"`
	KafkaConfig            KafkaConfig                     `yaml:"kafka"`
	EnvironmentConfig      EnvironmentConfig               `yaml:"environment"`
	MetricsConfig          MetricsConfig                   `yaml:"metrics"`
	AuthorizationConfig    AuthorizationConfig             `yaml:"authorization"`
	S3Config               S3Config                        `yaml:"s3Config"`
	UserBlockingConfig     map[string][]UserBlockingConfig `yaml:"user_blocking_config"`
	AppIdRequiredEndpoints []string                        `yaml:"appIdRequiredEndpoints"`
	TokenConfig            TokenConfig                     `yaml:"token"`
}
type TokenConfig struct {
	TokenExpiry   int64  `yaml:"tokenExpiry" envconfig:"TOKEN_EXPIRY"`
	SessionExpiry int64  `yaml:"sessionExpiry" envconfig:"SESSION_EXPIRY"`
	SecretKey     string `yaml:"secretkey" envconfig:"SECRET_KEY"`
}

var (
	config     *AppConfig
	configPath string
)

func loadConfig() (AppConfig, error) {
	f, err := os.Open(configPath)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	var cfg AppConfig
	decoder := yaml.NewDecoder(f)
	err = decoder.Decode(&cfg)
	err = envconfig.Process("", &cfg)
	if err != nil {
		panic(err)
	}

	return cfg, err
}

func GetConfig(path string) (AppConfig, error) {
	if config == nil {
		flag.StringVar(&configPath, "config", path, "Path to config file")
		flag.Parse()
		cnf, err := loadConfig()
		if err != nil {
			panic(err)
		}
		config = &cnf
	}
	return *config, nil
}
