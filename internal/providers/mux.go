package providers

import (
	"context"
	globals "discount-service/global/constant"
	"github.com/gorilla/mux"
	"net/http"

	authzGlobals "bitbucket.org/infracoreplatform/iam-authorization/globals"
	"bitbucket.org/infracoreplatform/iam-authorization/iamCompliance"
	authmiddleWare "bitbucket.org/infracoreplatform/server-utils/auth"
	"bitbucket.org/infracoreplatform/server-utils/cache"
	"bitbucket.org/infracoreplatform/server-utils/common"
	"bitbucket.org/infracoreplatform/server-utils/cors"
	jwtmiddleWare "bitbucket.org/infracoreplatform/server-utils/jwt"
	"bitbucket.org/infracoreplatform/server-utils/metrics"
	parser "bitbucket.org/infracoreplatform/server-utils/parser/v3"
	"github.com/dgrijalva/jwt-go"
	"github.com/justinas/alice"
)

func bypassAuth() func(http.Handler) http.Handler {
	return func(h http.Handler) http.Handler {
		return http.HandlerFunc(func(rw http.ResponseWriter, rq *http.Request) {
			*rq = *rq.WithContext(context.WithValue(rq.Context(), "request_user_id", 1))
			h.ServeHTTP(rw, rq)
		})
	}
}

func GetMux(apiRouter *mux.Router, config AppConfig, redisCache cache.RedisCacheInterface, logger common.LogglyLogger, loggerTracer metrics.CompositeObserver) (http.Handler, error) {
	// change env name in yml to bypass auth, or we can use dbHost, need input
	if config.DbConfig.Host == "localhost" {
		mux := alice.New(parser.RequestPayloadParserMiddleware(logger.GetLogrusEntry()), bypassAuth()).Then(nil)
		return cors.HandleCors([]string{config.HttpConfig.AllowedOrigin})(mux), nil
	}
	jwtMiddleware := jwtmiddleWare.New(jwtmiddleWare.Options{
		WhitelistedEndpoints: config.HttpConfig.WhiteList,
		ValidationKeyGetter: func(token *jwt.Token) (interface{}, error) {
			return []byte(config.EnvironmentConfig.Name + "_" + config.JwtConfig.Secret), nil
		},
		SigningMethod: jwt.SigningMethodHS256,
		SecretKey:     []byte(config.EnvironmentConfig.Name + "_" + config.JwtConfig.Secret),
		Cache:         redisCache,
	})
	authMiddleWare := authmiddleWare.New(authmiddleWare.Options{
		WhitelistedEndpoints: config.HttpConfig.WhiteList,
		Cache:                redisCache,
		Environment:          config.EnvironmentConfig.Name,
		JwtMiddleware:        jwtMiddleware,
		LoggerTracer:         loggerTracer,
	})

	var iamAuthz iamCompliance.Authz
	iamAuthzConfig, err := iamAuthz.Init(apiRouter, config.LogglyConfig.AppName, config.EnvironmentConfig.Name, config.HttpConfig.WhiteList, nil, redisCache, logger, loggerTracer)
	if err != nil && err.Error() != authzGlobals.ERR_BULK_POLICY_REGISTRATION {
		logger.WithField("err", err).Error("Error while initializing IAM authz")
		return nil, err
	}

	mux := alice.New(common.ResponseReportingMiddleware(logger.GetLogrusEntry()),
		jwtMiddleware.JwtHandler(logger.GetLogrusEntry()),
		parser.RequestPayloadParserMiddleware(logger.GetLogrusEntry()),
		authMiddleWare.AuthHandler(logger.GetLogrusEntry()),
		iamAuthz.Authorize(logger.GetLogrusEntry(), iamAuthzConfig),
	).Then(nil)

	return cors.HandleCors(globals.AllowedOrigins)(mux), nil
}
