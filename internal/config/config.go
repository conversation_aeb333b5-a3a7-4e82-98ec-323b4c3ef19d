package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// Config holds the application configuration
type Config struct {
	Port             string `json:"port"`
	UserServiceURL   string `json:"user_service_url"`
	GoogleCertsURL   string `json:"google_certs_url"`
	JWTIssuer        string `json:"jwt_issuer"`
	AnalyticsConfig  *AnalyticsConfig
	RolesConfig      *RolesConfig
}

// AnalyticsConfig represents the complete analytics configuration
type AnalyticsConfig struct {
	AnalyticalFunctions []AnalyticalFunction `json:"analyticalFunctions"`
}

// AnalyticalFunction represents a top-level analytics function
type AnalyticalFunction struct {
	ID               string             `json:"id"`
	Title            string             `json:"title"`
	Description      string             `json:"description"`
	Icon             string             `json:"icon"`
	BgColor          string             `json:"bgColor"`
	RequiredRoles    []string           `json:"requiredRoles"`
	BusinessFunction []BusinessFunction `json:"businessFunction"`
}

// BusinessFunction represents a business function within an analytics function
type BusinessFunction struct {
	ID            string          `json:"id"`
	Title         string          `json:"title"`
	Description   string          `json:"description"`
	Icon          string          `json:"icon"`
	RequiredRoles []string        `json:"requiredRoles"`
	AnalyticsType []AnalyticsType `json:"analyticsType"`
	Reports       bool            `json:"reports"`
	Data          bool            `json:"data"`
	Prediction    bool            `json:"prediction"`
}

// AnalyticsType represents a type of analytics within a business function
type AnalyticsType struct {
	ID            string      `json:"id"`
	Title         string      `json:"title"`
	Icon          string      `json:"icon"`
	RequiredRoles []string    `json:"requiredRoles"`
	Dashboard     []Dashboard `json:"dashboard"`
}

// Dashboard represents a dashboard within an analytics type
type Dashboard struct {
	ID            string   `json:"id"`
	Title         string   `json:"title"`
	URL           string   `json:"url"`
	RequiredRoles []string `json:"requiredRoles"`
}

// RolesConfig represents the roles configuration
type RolesConfig struct {
	Roles []Role `json:"roles"`
}

// Role represents a role definition
type Role struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions"`
}

// LoadConfig loads the application configuration
func LoadConfig() (*Config, error) {
	config := &Config{
		Port:           getEnv("PORT", "8081"),
		UserServiceURL: getEnv("USER_SERVICE_URL", "http://localhost:5173"),
		GoogleCertsURL: getEnv("GOOGLE_CERTS_URL", "https://www.googleapis.com/oauth2/v3/certs"),
		JWTIssuer:      getEnv("JWT_ISSUER", "accounts.google.com"),
	}

	// Load analytics configuration
	analyticsConfig, err := loadAnalyticsConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load analytics config: %w", err)
	}
	config.AnalyticsConfig = analyticsConfig

	// Load roles configuration
	rolesConfig, err := loadRolesConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to load roles config: %w", err)
	}
	config.RolesConfig = rolesConfig

	return config, nil
}

// loadAnalyticsConfig loads the analytics configuration from JSON file
func loadAnalyticsConfig() (*AnalyticsConfig, error) {
	file, err := os.ReadFile("config/analytics.json")
	if err != nil {
		return nil, err
	}

	var config AnalyticsConfig
	if err := json.Unmarshal(file, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// loadRolesConfig loads the roles configuration from JSON file
func loadRolesConfig() (*RolesConfig, error) {
	file, err := os.ReadFile("config/roles.json")
	if err != nil {
		return nil, err
	}

	var config RolesConfig
	if err := json.Unmarshal(file, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
