# Server Configuration
PORT=8081

# User Service Configuration
USER_SERVICE_URL=http://localhost:5173

# Google OAuth Configuration
GOOGLE_CERTS_URL=https://www.googleapis.com/oauth2/v3/certs
JWT_ISSUER=accounts.google.com

# CORS Configuration (comma-separated list)
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,https://tesseract-web.vercel.app

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
JWT_EXPIRY_BUFFER_MINUTES=5
TOKEN_REFRESH_THRESHOLD_MINUTES=10
