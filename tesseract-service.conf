location ~ ^/ping {
  return 200 'bazinga!';
}

location ~ ^/version {
  return 200 '1';
}

location ~ ^/tesseract/ {
  add_header Allow "GET, POST, PUT, OPTIONS, HEAD, DELETE" always;

  if ( $request_method !~ ^(GET|POST|PUT|OPTIONS|HEAD|DELETE)$ ) {
    return 405;
  }

  # Mechanism to apply Content-Length and Content-Type checks only for POST & PUT requests.
  #
  # Working:
  # Since nginx does not allow multiple conditions in an if statement,
  # we will set a decider-variable(of type string) in a piece-by-piece fashion to particular value based on:
  # 1. the type of the incoming request type
  # 2. whether it has passed a particular check or not(for example a content-type check).
  #
  # Once this decider variable is set ,we then do a final check for the value of this variable.
  # Based upon the result of that check, we decide what to do with the request.

  set $decider_string_content_type_based_blockage "";
  set $decider_string_content_length_based_blockage "";
  if ($request_method ~ ^(POST|PUT)$ ){
    set $decider_string_content_length_based_blockage write_type_request;
    set $decider_string_content_type_based_blockage write_type_request;
  }

  if ($content_length ~ ^(0|1|2|)$ ){
    set $decider_string_content_length_based_blockage "${decider_string_content_length_based_blockage}_invalid_content_length";
  }

  if ($decider_string_content_length_based_blockage = write_type_request_invalid_content_length){
    # Returning 412 response code if request type is either POST or PUT and
    # request has passed the invalid content-length check(specified above)
    return 412;
  }

  if ($content_type !~ '^application/json(; charset=utf-8)?$' ) {
    set $decider_string_content_type_based_blockage "${decider_string_content_type_based_blockage}_invalid_content_type";
  }
  if ($decider_string_content_type_based_blockage = write_type_request_invalid_content_type) {
    # Returning 412 response code if request type is either POST or PUT and
    # request has passed the invalid content-type check(specified above)
    return 412;
  }

  client_max_body_size 32M;
  proxy_set_header Host $http_host;
  proxy_set_header X-Forwarded-For $http_x_forwarded_for;

  rewrite /tesseract/(.+) /$1 break;
  proxy_redirect off;
  proxy_pass http://127.0.0.1:9017;
}
