package main

import (
	"log"
	"os"

	"tesseract-service/internal/api"
	"tesseract-service/internal/config"
	"tesseract-service/internal/services"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize services
	authService := services.NewAuthService(cfg)
	configService := services.NewConfigService(cfg)
	userService := services.NewUserService(cfg)

	// Initialize Gin router
	router := gin.Default()

	// Configure CORS
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{
		"http://localhost:8080", // Frontend
		"http://localhost:3000",
		"https://tesseract-web.vercel.app", // Add your production frontend URL
	}
	corsConfig.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-Requested-With",
	}
	corsConfig.AllowMethods = []string{
		"GET",
		"POST",
		"PUT",
		"DELETE",
		"OPTIONS",
	}
	corsConfig.AllowCredentials = true

	router.Use(cors.New(corsConfig))

	// Initialize API handlers
	apiHandler := api.NewHandler(authService, configService, userService)

	// Setup routes
	v1 := router.Group("/api/v1")
	{
		// Health check
		v1.GET("/health", apiHandler.HealthCheck)

		// Authentication routes
		auth := v1.Group("/auth")
		{
			auth.POST("/validate", apiHandler.ValidateToken)
		}

		// Configuration routes (protected)
		configGroup := v1.Group("/config")
		configGroup.Use(apiHandler.AuthMiddleware())
		{
			configGroup.GET("/analytics", apiHandler.GetAnalyticsConfig)
			configGroup.GET("/business-functions/:analyticsFunction", apiHandler.GetBusinessFunctions)
			configGroup.GET("/analytics-types/:analyticsFunction/:businessFunction", apiHandler.GetAnalyticsTypes)
		}

		// User routes (protected)
		user := v1.Group("/user")
		user.Use(apiHandler.AuthMiddleware())
		{
			user.GET("/profile", apiHandler.GetUserProfile)
			user.GET("/permissions", apiHandler.GetUserPermissions)
		}
	}

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8081"
	}

	log.Printf("Starting Tesseract Service on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
