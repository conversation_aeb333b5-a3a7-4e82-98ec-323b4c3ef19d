# Tesseract Service

Phoenix Tesseract Analytics Backend Service

## Overview
This is the backend service for Phoenix Tesseract Analytics platform, built with Go. It provides:
- JWT token validation
- Role-based access control
- Analytics configuration management
- User permission filtering

## Architecture
- **Framework**: Gin (Go web framework)
- **Authentication**: JWT validation via Google public keys
- **Configuration**: JSON-based config files
- **User Service Integration**: REST API calls to user-service

## API Endpoints
- `GET /api/v1/health` - Health check
- `POST /api/v1/auth/validate` - Validate JWT token
- `GET /api/v1/config/analytics` - Get filtered analytics configuration
- `GET /api/v1/config/business-functions/:analyticsFunction` - Get business functions
- `GET /api/v1/config/analytics-types/:analyticsFunction/:businessFunction` - Get analytics types

## Environment Variables
- `PORT` - Server port (default: 8081)
- `USER_SERVICE_URL` - URL of the user service (default: http://localhost:5173)
- `GOOGLE_CERTS_URL` - Google public keys URL
- `JWT_ISSUER` - Expected JWT issuer (accounts.google.com)

## Running the Service
```bash
go mod tidy
go run main.go
```

## Configuration Structure
The service uses JSON configuration files stored in the `config/` directory:
- `analytics.json` - Complete analytics configuration with permissions
- `roles.json` - Role definitions and mappings

## Role-Based Access Control
Roles follow the pattern: `ANALYTICSFUNCTION_BUSINESSFUNCTION_MODULE_TYPE_NAVIGATION`
Example: `SALES_SALESPRIMARY_ANALYTICS_PERFORMANCE_ANALYTICS_SALESSUMMARY`
