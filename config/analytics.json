{"analyticalFunctions": [{"id": "risk", "title": "Risk Analytics", "description": "Risk assessment and insights", "icon": "Activity", "bgColor": "green", "roles": [], "businessFunction": [{"id": "credit-risk", "title": "Credit Risk Management", "description": "Credit assessment, risk scoring, and default prediction analytics", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roles": ["TESSERACT_RISK_SCORE_TRENDS", "TESSERACT_RISK_CATEGORY_DISTRIBUTION"], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": ["TESSERACT_RISK_SCORE_TRENDS", "TESSERACT_RISK_CATEGORY_DISTRIBUTION"], "dashboard": [{"id": "risk-score", "title": "Risk Score Trends", "url": "https://lookerstudio.google.com/embed/reporting/d8f0ccc5-7f09-4ea6-a61a-13b0fbb861e5/page/ifTAF", "roles": ["TESSERACT_RISK_SCORE_TRENDS"]}, {"id": "risk-category", "title": "Risk Category Distribution", "url": "https://lookerstudio.google.com/embed/reporting/6eb63ccf-0c6e-4843-bda1-f463f4459361/page/p_9mfi6muz8c", "roles": ["TESSERACT_RISK_CATEGORY_DISTRIBUTION"]}, {"id": "performance-metrics", "title": "Performance Metrics", "url": "https://lookerstudio.google.com/embed/reporting/0a00bef2-453b-48a0-9304-56b4339dcb96/page/p_xxxwh1v27c", "roles": ["RISK_CREDITRISK_ANALYTICS_PERFORMANCE_ANALYTICS_PERFORMANCEMETRICS"]}, {"id": "historical-comparison", "title": "Historical Comparison", "url": "https://lookerstudio.google.com/embed/reporting/d8f0ccc5-7f09-4ea6-a61a-13b0fbb861e5/page/ifTAF", "roles": ["RISK_CREDITRISK_ANALYTICS_PERFORMANCE_ANALYTICS_HISTORICALCOMPARISON"]}]}, {"id": "lifecycle-analysis", "title": "Lifecycle Analysis", "icon": "Activity", "roles": [], "dashboard": []}, {"id": "operational-efficiency", "title": "Operational Efficiency", "icon": "<PERSON><PERSON><PERSON>", "roles": [], "dashboard": []}, {"id": "anomaly-detection", "title": "Anomaly Detection", "icon": "OctagonAlert", "roles": [], "dashboard": []}], "reports": true, "data": false, "prediction": false}, {"id": "credit-analysis", "title": "Credit Analysis", "description": "Detailed credit evaluation and supply chain credit monitoring", "icon": "TrendingUp", "roles": [], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": [], "dashboard": []}, {"id": "lifecycle-analysis", "title": "Lifecycle Analysis", "icon": "Activity", "roles": [], "dashboard": [{"id": "credit-supply-orders", "title": "Credit Supply Orders", "url": "https://lookerstudio.google.com/embed/reporting/b39271f0-f6f5-40c4-810c-e464ab263b4a/page/3NIPD", "roles": ["RISK_CREDITANALYSIS_ANALYTICS_LIFECYCLE_ANALYSIS_CREDITSUPPLYORDERS"]}]}, {"id": "operational-efficiency", "title": "Operational Efficiency", "icon": "<PERSON><PERSON><PERSON>", "roles": [], "dashboard": []}, {"id": "anomaly-detection", "title": "Anomaly Detection", "icon": "OctagonAlert", "roles": [], "dashboard": []}], "reports": false, "data": false, "prediction": false}]}, {"id": "sales", "title": "Sales Analytics", "description": "Sales performance and trends", "icon": "Line<PERSON>hart", "bgColor": "purple", "roles": [], "businessFunction": [{"id": "pre-sales", "title": "Pre-Sales Analytics", "description": "Lead qualification, opportunity analysis, and prospect insights", "icon": "Target", "roles": [], "analyticsType": [], "reports": false, "data": false, "prediction": false}, {"id": "sales-primary", "title": "Primary Sales", "description": "Core sales performance, deal tracking, and revenue analytics", "icon": "DollarSign", "roles": [], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": [], "dashboard": [{"id": "sales-summary", "title": "Sales Summary", "url": "https://lookerstudio.google.com/embed/reporting/d8f0ccc5-7f09-4ea6-a61a-13b0fbb861e5/page/p_3u3rq8grqd", "roles": ["SALES_SALESPRIMARY_ANALYTICS_PERFORMANCE_ANALYTICS_SALESSUMMARY"]}, {"id": "status-by-org-hierarchy", "title": "Sales by Org Hierarchy", "url": "https://lookerstudio.google.com/embed/reporting/bdbcbd23-a578-4c77-b9ae-671832329465/page/p_r2jlozfagd", "roles": ["SALES_SALESPRIMARY_ANALYTICS_PERFORMANCE_ANALYTICS_STATUSBYORGHIERARCHY"]}, {"id": "deals-by-status", "title": "Deals by Status", "url": "https://lookerstudio.google.com/embed/reporting/d8f0ccc5-7f09-4ea6-a61a-13b0fbb861e5/page/p_sqyb47qlqd", "roles": ["SALES_SALESPRIMARY_ANALYTICS_PERFORMANCE_ANALYTICS_DEALSBYSTATUS"]}]}], "reports": true, "data": true, "prediction": true}]}, {"id": "supply-chain", "title": "Supply Chain Analytics", "description": "Supply chain performance metrics", "icon": "ShoppingCart", "bgColor": "blue", "roles": [], "businessFunction": [{"id": "inventory-management", "title": "Inventory Management", "description": "Stock levels, inventory turnover, and warehouse optimization", "icon": "Package", "roles": [], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": [], "dashboard": [{"id": "psn-order-status", "title": "PSN Order Status", "url": "https://lookerstudio.google.com/embed/reporting/8a49d451-b987-4c0b-b86c-c61a64802fea/page/p_e1rlewxled", "roles": ["SUPPLYCHAIN_INVENTORY_ANALYTICS_PERFORMANCE_ANALYTICS_PSNORDERSTATUS"]}]}, {"id": "operational-efficiency", "title": "Operational Efficiency", "icon": "<PERSON><PERSON><PERSON>", "roles": [], "dashboard": [{"id": "plant-operations-and-accounts-info", "title": "Plant Operations and Accounts Info", "url": "https://lookerstudio.google.com/embed/reporting/de7f5eb1-359e-4630-87a7-5aff291b1b2d/page/XOMVD", "roles": ["SUPPLYCHAIN_INVENTORY_ANALYTICS_OPERATIONAL_EFFICIENCY_PLANTOPERATIONS"]}]}], "reports": true, "data": false, "prediction": false}, {"id": "warehouse-management", "title": "Warehouse Management", "description": "Warehouse operations, space utilization, and fulfillment analytics", "icon": "Warehouse", "roles": [], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": [], "dashboard": [{"id": "auto-warehouse-allocation", "title": "Auto Warehouse Allocation", "url": "https://lookerstudio.google.com/embed/reporting/********-f9ca-4c93-a876-50851d45a554/page/8o22D", "roles": ["SUPPLYCHAIN_WAREHOUSE_ANALYTICS_PERFORMANCE_ANALYTICS_AUTOWAREHOUSEALLOCATION"]}]}], "reports": true, "data": false, "prediction": false}]}, {"id": "tech-adoption", "title": "Tech Adoption", "description": "Technology usage metrics", "icon": "Cpu", "bgColor": "indigo", "roles": [], "businessFunction": [{"id": "tech-adoption", "title": "Technology Adoption", "description": "Digital transformation metrics, tool usage, and adoption rates", "icon": "Smartphone", "roles": [], "analyticsType": [{"id": "performance-analytics", "title": "Performance Analytics", "icon": "Line<PERSON>hart", "roles": [], "dashboard": [{"id": "adoption-summary", "title": "Adoption Summary", "url": "https://lookerstudio.google.com/embed/reporting/317ca109-fa71-4562-982c-21e3547247c2/page/p_jfha68ldrd", "roles": ["TECHADOPTION_TECHADOPTION_ANALYTICS_PERFORMANCE_ANALYTICS_ADOPTIONSUMMARY"]}, {"id": "adoption-trends", "title": "Adoption Trends", "url": "https://lookerstudio.google.com/embed/reporting/317ca109-fa71-4562-982c-21e3547247c2/page/p_61gx6hmdrd", "roles": ["TECHADOPTION_TECHADOPTION_ANALYTICS_PERFORMANCE_ANALYTICS_ADOPTIONTRENDS"]}]}], "reports": true, "data": false, "prediction": false}]}]}